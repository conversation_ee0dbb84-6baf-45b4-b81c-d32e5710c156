'use client';

import { CurrencyStats as CurrencyStatsType } from '../currency.model';

interface CurrencyStatsProps {
  stats: CurrencyStatsType | null;
  loading: boolean;
}

export default function CurrencyStats({ stats, loading }: CurrencyStatsProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const statCards = [
    {
      title: 'Total Currencies',
      value: stats?.totalCurrencies || 0,
      icon: 'ri-coins-line',
      color: 'blue',
      description: 'All configured currencies'
    },
    {
      title: 'Active Currencies',
      value: stats?.activeCurrencies || 0,
      icon: 'ri-check-line',
      color: 'green',
      description: 'Currently enabled currencies'
    },
    {
      title: 'Disabled Currencies',
      value: stats?.disabledCurrencies || 0,
      icon: 'ri-close-line',
      color: 'red',
      description: 'Disabled currencies'
    }
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: {
        bg: 'bg-blue-100',
        text: 'text-blue-600',
        icon: 'text-blue-600'
      },
      green: {
        bg: 'bg-green-100',
        text: 'text-green-600',
        icon: 'text-green-600'
      },
      red: {
        bg: 'bg-red-100',
        text: 'text-red-600',
        icon: 'text-red-600'
      },
      yellow: {
        bg: 'bg-yellow-100',
        text: 'text-yellow-600',
        icon: 'text-yellow-600'
      }
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="animate-pulse">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gray-200 rounded-lg mr-4"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded mb-1"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statCards.map((card, index) => {
          const colors = getColorClasses(card.color);
          return (
            <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className={`w-12 h-12 ${colors.bg} rounded-lg flex items-center justify-center mr-4`}>
                  <i className={`${card.icon} ${colors.icon} text-xl`}></i>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-600 truncate">{card.title}</p>
                  <h3 className="text-2xl font-bold text-gray-900 truncate">{card.value}</h3>
                  <p className="text-xs text-gray-500 truncate">{card.description}</p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Additional Info */}
      {stats && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Currency System Status</h3>
              <p className="text-sm text-gray-600">Overview of your currency configuration</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-500">Last Updated</p>
              <p className="text-sm font-medium text-gray-900">
                {formatDate(stats.lastUpdated)}
              </p>
            </div>
          </div>

          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                <i className="ri-global-line text-blue-600"></i>
              </div>
              <p className="text-sm text-gray-600">Coverage</p>
              <p className="text-lg font-semibold text-gray-900">
                {Math.round((stats.activeCurrencies / stats.totalCurrencies) * 100)}%
              </p>
              <p className="text-xs text-gray-500">Active currencies</p>
            </div>

            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                <i className="ri-shield-check-line text-green-600"></i>
              </div>
              <p className="text-sm text-gray-600">System Health</p>
              <p className="text-lg font-semibold text-green-600">Healthy</p>
              <p className="text-xs text-gray-500">All systems operational</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
