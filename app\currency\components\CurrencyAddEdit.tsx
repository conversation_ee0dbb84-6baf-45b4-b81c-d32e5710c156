'use client';

import { useState, useEffect } from 'react';
import { Currency, CurrencyFormData, COMMON_CURRENCIES } from '../currency.model';
import CurrencyService from '../currency.service';

interface CurrencyAddEditProps {
  currency: Currency | null;
  onSave: () => void;
  onCancel: () => void;
}

export default function CurrencyAddEdit({ currency, onSave, onCancel }: CurrencyAddEditProps) {
  const [formData, setFormData] = useState<CurrencyFormData>({
    code: '',
    name: '',
    symbol: '',
    amount: 1.0,
    isActive: true,
    isBaseCurrency: false,
    decimalPlaces: 2
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [availableCurrencies, setAvailableCurrencies] = useState<typeof COMMON_CURRENCIES>([]);

  useEffect(() => {
    if (currency) {
      setFormData({
        code: currency.code,
        name: currency.name,
        symbol: currency.symbol,
        amount: currency.amount,
        isActive: currency.isActive,
        isBaseCurrency: currency.isBaseCurrency,
        decimalPlaces: currency.decimalPlaces
      });
    } else {
      // Load available currencies for new currency creation
      loadAvailableCurrencies();
    }
  }, [currency]);

  const loadAvailableCurrencies = async () => {
    try {
      const available = await CurrencyService.getAvailableCurrencyCodes();
      setAvailableCurrencies(available);
    } catch (error) {
      console.error('Error loading available currencies:', error);
    }
  };

  const handleInputChange = (field: keyof CurrencyFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleCurrencySelect = (selectedCurrency: typeof COMMON_CURRENCIES[0]) => {
    setFormData(prev => ({
      ...prev,
      code: selectedCurrency.code,
      name: selectedCurrency.name,
      symbol: selectedCurrency.symbol,
      decimalPlaces: selectedCurrency.decimalPlaces
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.code.trim()) {
      newErrors.code = 'Currency code is required';
    } else if (!/^[A-Z]{3}$/.test(formData.code)) {
      newErrors.code = 'Currency code must be 3 uppercase letters (ISO 4217)';
    }

    if (!formData.name.trim()) {
      newErrors.name = 'Currency name is required';
    }

    if (!formData.symbol.trim()) {
      newErrors.symbol = 'Currency symbol is required';
    }

    if (formData.amount <= 0) {
      newErrors.amount = 'Exchange rate must be greater than 0';
    }

    if (formData.decimalPlaces < 0 || formData.decimalPlaces > 4) {
      newErrors.decimalPlaces = 'Decimal places must be between 0 and 4';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      if (currency) {
        await CurrencyService.updateCurrency(currency.id, formData);
      } else {
        await CurrencyService.createCurrency(formData);
      }
      onSave();
    } catch (error: any) {
      setErrors({ submit: error.message || 'An error occurred while saving the currency' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Currency Selection (for new currencies) */}
      {!currency && availableCurrencies.length > 0 && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Quick Select Currency
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-32 overflow-y-auto border border-gray-200 rounded-lg p-2">
            {availableCurrencies.map((curr) => (
              <button
                key={curr.code}
                type="button"
                onClick={() => handleCurrencySelect(curr)}
                className="text-left p-2 rounded hover:bg-gray-100 border border-gray-200 text-sm"
              >
                <div className="font-medium">{curr.code}</div>
                <div className="text-gray-500 text-xs">{curr.name}</div>
              </button>
            ))}
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Select a currency to auto-fill the form, or fill manually below
          </p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Currency Code */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Currency Code *
          </label>
          <input
            type="text"
            value={formData.code}
            onChange={(e) => handleInputChange('code', e.target.value.toUpperCase())}
            placeholder="USD"
            maxLength={3}
            className={`block w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm ${
              errors.code ? 'border-red-300' : 'border-gray-300'
            }`}
            disabled={!!currency} // Disable editing code for existing currencies
          />
          {errors.code && <p className="text-red-500 text-xs mt-1">{errors.code}</p>}
        </div>

        {/* Currency Symbol */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Currency Symbol *
          </label>
          <input
            type="text"
            value={formData.symbol}
            onChange={(e) => handleInputChange('symbol', e.target.value)}
            placeholder="$"
            className={`block w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm ${
              errors.symbol ? 'border-red-300' : 'border-gray-300'
            }`}
          />
          {errors.symbol && <p className="text-red-500 text-xs mt-1">{errors.symbol}</p>}
        </div>
      </div>

      {/* Currency Name */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Currency Name *
        </label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => handleInputChange('name', e.target.value)}
          placeholder="US Dollar"
          className={`block w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm ${
            errors.name ? 'border-red-300' : 'border-gray-300'
          }`}
        />
        {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Exchange Rate */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Exchange Rate *
          </label>
          <input
            type="number"
            step="0.000001"
            min="0"
            value={formData.amount}
            onChange={(e) => handleInputChange('amount', parseFloat(e.target.value) || 0)}
            placeholder="1.0"
            className={`block w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm ${
              errors.amount ? 'border-red-300' : 'border-gray-300'
            }`}
          />
          {errors.amount && <p className="text-red-500 text-xs mt-1">{errors.amount}</p>}
          <p className="text-xs text-gray-500 mt-1">Rate relative to base currency</p>
        </div>

        {/* Decimal Places */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Decimal Places *
          </label>
          <select
            value={formData.decimalPlaces}
            onChange={(e) => handleInputChange('decimalPlaces', parseInt(e.target.value))}
            className={`block w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm ${
              errors.decimalPlaces ? 'border-red-300' : 'border-gray-300'
            }`}
          >
            <option value={0}>0 (e.g., ¥100)</option>
            <option value={2}>2 (e.g., $1.00)</option>
            <option value={3}>3 (e.g., KWD 1.000)</option>
            <option value={4}>4 (e.g., BTC 0.0001)</option>
          </select>
          {errors.decimalPlaces && <p className="text-red-500 text-xs mt-1">{errors.decimalPlaces}</p>}
        </div>
      </div>

      {/* Checkboxes */}
      <div className="space-y-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="isActive"
            checked={formData.isActive}
            onChange={(e) => handleInputChange('isActive', e.target.checked)}
            className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
          />
          <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
            Active Currency
          </label>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="isBaseCurrency"
            checked={formData.isBaseCurrency}
            onChange={(e) => handleInputChange('isBaseCurrency', e.target.checked)}
            className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
          />
          <label htmlFor="isBaseCurrency" className="ml-2 block text-sm text-gray-700">
            Set as Base Currency
          </label>
          <p className="ml-2 text-xs text-gray-500">(Only one base currency allowed)</p>
        </div>
      </div>

      {/* Submit Error */}
      {errors.submit && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <p className="text-red-600 text-sm">{errors.submit}</p>
        </div>
      )}

      {/* Form Actions */}
      <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Saving...
            </div>
          ) : (
            currency ? 'Update Currency' : 'Create Currency'
          )}
        </button>
      </div>
    </form>
  );
}
