'use client';

import { useState, useEffect } from 'react';
import { Currency, CurrencyFilters, CurrencyStats } from '../currency.model';
import CurrencyService from '../currency.service';
import CurrencyStats from './CurrencyStats';
import CurrencyList from './CurrencyList';
import CurrencyAddEdit from './CurrencyAddEdit';
import CurrencyView from './CurrencyView';
import CurrencyFilters from './CurrencyFilters';
import Modal from '../../components/ui/Modal';

export default function CurrencyMaster() {
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  const [selectedCurrency, setSelectedCurrency] = useState<Currency | null>(null);
  const [isAddEditOpen, setIsAddEditOpen] = useState(false);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [stats, setStats] = useState<CurrencyStats | null>(null);
  const [filters, setFilters] = useState<CurrencyFilters>({
    search: '',
    isActive: undefined,
    isBaseCurrency: undefined,
    codeFilter: ''
  });

  // Fetch currencies
  const fetchCurrencies = async () => {
    try {
      setLoading(true);
      const response = await CurrencyService.getAllCurrencies(currentPage, pageSize, filters);
      setCurrencies(response.data);
      setTotalPages(response.pagination.total_pages);
      setTotalItems(response.pagination.total_items);
    } catch (error) {
      console.error('Error fetching currencies:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch stats
  const fetchStats = async () => {
    try {
      const statsData = await CurrencyService.getCurrencyStats();
      setStats(statsData);
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  useEffect(() => {
    fetchCurrencies();
    fetchStats();
  }, [currentPage, pageSize, filters]);

  // Handle create currency
  const handleCreateCurrency = () => {
    setSelectedCurrency(null);
    setIsAddEditOpen(true);
  };

  // Handle edit currency
  const handleEditCurrency = (currency: Currency) => {
    setSelectedCurrency(currency);
    setIsAddEditOpen(true);
  };

  // Handle view currency
  const handleViewCurrency = (currency: Currency) => {
    setSelectedCurrency(currency);
    setIsViewOpen(true);
  };

  // Handle delete currency
  const handleDeleteCurrency = async (currency: Currency) => {
    if (window.confirm(`Are you sure you want to delete ${currency.name} (${currency.code})?`)) {
      try {
        await CurrencyService.deleteCurrency(currency.id);
        fetchCurrencies();
        fetchStats();
      } catch (error) {
        console.error('Error deleting currency:', error);
        alert('Error deleting currency. Please try again.');
      }
    }
  };

  // Handle toggle status
  const handleToggleStatus = async (currency: Currency) => {
    try {
      await CurrencyService.toggleCurrencyStatus(currency.id);
      fetchCurrencies();
      fetchStats();
    } catch (error) {
      console.error('Error toggling currency status:', error);
      alert('Error updating currency status. Please try again.');
    }
  };

  // Handle save currency
  const handleSaveCurrency = async () => {
    setIsAddEditOpen(false);
    await fetchCurrencies();
    await fetchStats();
  };

  // Handle filters change
  const handleFiltersChange = (newFilters: CurrencyFilters) => {
    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle page size change
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  return (
    <div className="space-y-6">
      {/* Currency Statistics */}
      <CurrencyStats stats={stats} loading={loading} />

      {/* Header with Actions */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Currency Management</h2>
          <p className="text-gray-600">
            Manage exchange rates and currency configurations
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <span className="text-sm text-gray-500">
            Total: {totalItems} currencies
          </span>
          <button
            onClick={handleCreateCurrency}
            className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
          >
            <i className="ri-add-line mr-2"></i>
            Add Currency
          </button>
        </div>
      </div>

      {/* Filters */}
      <CurrencyFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
      />

      {/* Currency List */}
      <CurrencyList
        currencies={currencies}
        onEdit={handleEditCurrency}
        onView={handleViewCurrency}
        onDelete={handleDeleteCurrency}
        onToggleStatus={handleToggleStatus}
        loading={loading}
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        totalItems={totalItems}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
      />

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isAddEditOpen}
        onClose={() => setIsAddEditOpen(false)}
        title={selectedCurrency ? 'Edit Currency' : 'Add New Currency'}
        subtitle={selectedCurrency ? `Update ${selectedCurrency.name} details` : 'Add a new currency to the system'}
        size="lg"
      >
        <CurrencyAddEdit
          currency={selectedCurrency}
          onSave={handleSaveCurrency}
          onCancel={() => setIsAddEditOpen(false)}
        />
      </Modal>

      {/* View Modal */}
      <Modal
        isOpen={isViewOpen}
        onClose={() => setIsViewOpen(false)}
        title="Currency Details"
        subtitle={selectedCurrency ? `${selectedCurrency.name} (${selectedCurrency.code})` : ''}
        size="lg"
      >
        <CurrencyView
          currency={selectedCurrency}
          onEdit={() => {
            setIsViewOpen(false);
            setIsAddEditOpen(true);
          }}
          onClose={() => setIsViewOpen(false)}
        />
      </Modal>
    </div>
  );
}
