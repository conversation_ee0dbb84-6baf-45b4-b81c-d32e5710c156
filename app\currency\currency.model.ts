// Currency Management Models and Types

export interface Currency {
  id: string;
  code: string; // ISO 4217 currency code (e.g., USD, EUR, GBP)
  name: string; // Full currency name (e.g., US Dollar, Euro, British Pound)
  symbol: string; // Currency symbol (e.g., $, €, £)
  amount: number; // Current exchange rate or amount
  isActive: boolean;
  isBaseCurrency: boolean; // Whether this is the base currency for the system
  decimalPlaces: number; // Number of decimal places for this currency
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
}

export interface CurrencyFormData {
  code: string;
  name: string;
  symbol: string;
  amount: number;
  isActive: boolean;
  isBaseCurrency: boolean;
  decimalPlaces: number;
}

export interface CurrencyFilters {
  search: string;
  isActive?: boolean;
  isBaseCurrency?: boolean;
  codeFilter: string;
}

export interface CurrencyStats {
  totalCurrencies: number;
  activeCurrencies: number;
  inactiveCurrencies: number;
  baseCurrency: string;
  lastUpdated: string;
}

export interface Pagination {
  current_page: number;
  page_size: number;
  total_items: number;
  total_pages: number;
  has_next: boolean;
  has_previous: boolean;
}

export interface CurrencyResponse {
  data: Currency[];
  pagination: Pagination;
  success: boolean;
  message?: string;
}

// Predefined currency data based on common international currencies
export const COMMON_CURRENCIES = [
  { code: 'USD', name: 'US Dollar', symbol: '$', decimalPlaces: 2 },
  { code: 'EUR', name: 'Euro', symbol: '€', decimalPlaces: 2 },
  { code: 'GBP', name: 'British Pound', symbol: '£', decimalPlaces: 2 },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥', decimalPlaces: 0 },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$', decimalPlaces: 2 },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$', decimalPlaces: 2 },
  { code: 'CHF', name: 'Swiss Franc', symbol: 'CHF', decimalPlaces: 2 },
  { code: 'CNY', name: 'Chinese Yuan', symbol: '¥', decimalPlaces: 2 },
  { code: 'SEK', name: 'Swedish Krona', symbol: 'kr', decimalPlaces: 2 },
  { code: 'NZD', name: 'New Zealand Dollar', symbol: 'NZ$', decimalPlaces: 2 },
  { code: 'MXN', name: 'Mexican Peso', symbol: '$', decimalPlaces: 2 },
  { code: 'SGD', name: 'Singapore Dollar', symbol: 'S$', decimalPlaces: 2 },
  { code: 'HKD', name: 'Hong Kong Dollar', symbol: 'HK$', decimalPlaces: 2 },
  { code: 'NOK', name: 'Norwegian Krone', symbol: 'kr', decimalPlaces: 2 },
  { code: 'KRW', name: 'South Korean Won', symbol: '₩', decimalPlaces: 0 },
  { code: 'TRY', name: 'Turkish Lira', symbol: '₺', decimalPlaces: 2 },
  { code: 'RUB', name: 'Russian Ruble', symbol: '₽', decimalPlaces: 2 },
  { code: 'INR', name: 'Indian Rupee', symbol: '₹', decimalPlaces: 2 },
  { code: 'BRL', name: 'Brazilian Real', symbol: 'R$', decimalPlaces: 2 },
  { code: 'ZAR', name: 'South African Rand', symbol: 'R', decimalPlaces: 2 },
  { code: 'PLN', name: 'Polish Zloty', symbol: 'zł', decimalPlaces: 2 },
  { code: 'DKK', name: 'Danish Krone', symbol: 'kr', decimalPlaces: 2 },
  { code: 'CZK', name: 'Czech Koruna', symbol: 'Kč', decimalPlaces: 2 },
  { code: 'HUF', name: 'Hungarian Forint', symbol: 'Ft', decimalPlaces: 0 },
  { code: 'ILS', name: 'Israeli Shekel', symbol: '₪', decimalPlaces: 2 },
  { code: 'CLP', name: 'Chilean Peso', symbol: '$', decimalPlaces: 0 },
  { code: 'PHP', name: 'Philippine Peso', symbol: '₱', decimalPlaces: 2 },
  { code: 'AED', name: 'UAE Dirham', symbol: 'د.إ', decimalPlaces: 2 },
  { code: 'COP', name: 'Colombian Peso', symbol: '$', decimalPlaces: 2 },
  { code: 'SAR', name: 'Saudi Riyal', symbol: '﷼', decimalPlaces: 2 },
  { code: 'MYR', name: 'Malaysian Ringgit', symbol: 'RM', decimalPlaces: 2 },
  { code: 'RON', name: 'Romanian Leu', symbol: 'lei', decimalPlaces: 2 },
  { code: 'HRK', name: 'Croatian Kuna', symbol: 'kn', decimalPlaces: 2 },
  { code: 'BGN', name: 'Bulgarian Lev', symbol: 'лв', decimalPlaces: 2 },
  { code: 'THB', name: 'Thai Baht', symbol: '฿', decimalPlaces: 2 },
  { code: 'IDR', name: 'Indonesian Rupiah', symbol: 'Rp', decimalPlaces: 0 },
  { code: 'EGP', name: 'Egyptian Pound', symbol: '£', decimalPlaces: 2 },
  { code: 'QAR', name: 'Qatari Riyal', symbol: '﷼', decimalPlaces: 2 },
  { code: 'KWD', name: 'Kuwaiti Dinar', symbol: 'د.ك', decimalPlaces: 3 },
  { code: 'BHD', name: 'Bahraini Dinar', symbol: '.د.ب', decimalPlaces: 3 }
];

// Currency utility functions
export const CurrencyUtils = {
  // Format currency amount with proper symbol and decimal places
  formatAmount: (amount: number, currency: Currency): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.code,
      minimumFractionDigits: currency.decimalPlaces,
      maximumFractionDigits: currency.decimalPlaces
    }).format(amount);
  },

  // Format amount with custom symbol
  formatAmountWithSymbol: (amount: number, symbol: string, decimalPlaces: number): string => {
    const formattedAmount = amount.toFixed(decimalPlaces);
    return `${symbol}${formattedAmount}`;
  },

  // Get currency by code
  getCurrencyByCode: (currencies: Currency[], code: string): Currency | undefined => {
    return currencies.find(currency => currency.code === code);
  },

  // Validate currency code format (ISO 4217)
  isValidCurrencyCode: (code: string): boolean => {
    return /^[A-Z]{3}$/.test(code);
  },

  // Get base currency from list
  getBaseCurrency: (currencies: Currency[]): Currency | undefined => {
    return currencies.find(currency => currency.isBaseCurrency);
  },

  // Sort currencies by code
  sortByCode: (currencies: Currency[]): Currency[] => {
    return [...currencies].sort((a, b) => a.code.localeCompare(b.code));
  },

  // Sort currencies by name
  sortByName: (currencies: Currency[]): Currency[] => {
    return [...currencies].sort((a, b) => a.name.localeCompare(b.name));
  },

  // Filter active currencies
  getActiveCurrencies: (currencies: Currency[]): Currency[] => {
    return currencies.filter(currency => currency.isActive);
  },

  // Convert amount between currencies (basic conversion)
  convertAmount: (amount: number, fromCurrency: Currency, toCurrency: Currency): number => {
    // This is a simplified conversion. In real implementation, you'd use actual exchange rates
    const baseAmount = amount / fromCurrency.amount;
    return baseAmount * toCurrency.amount;
  }
};

export default Currency;
