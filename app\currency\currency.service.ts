import { Cur<PERSON>cy, CurrencyFormData, CurrencyResponse, CurrencyFilters, COMMON_CURRENCIES } from './currency.model';

// Mock data for development - replace with actual API calls
const generateMockCurrencies = (): Currency[] => {
  return COMMON_CURRENCIES.slice(0, 15).map((currencyData, index) => ({
    id: `curr_${index + 1}`,
    code: currencyData.code,
    name: currencyData.name,
    symbol: currencyData.symbol,
    amount: Math.random() * 10 + 0.5, // Random exchange rate
    isActive: Math.random() > 0.2, // 80% chance of being active
    isBaseCurrency: currencyData.code === 'USD', // USD as base currency
    decimalPlaces: currencyData.decimalPlaces,
    created_at: new Date(Date.now() - Math.random() * 10000000000).toISOString(),
    updated_at: new Date().toISOString(),
    created_by: 'system',
    updated_by: 'admin'
  }));
};

let mockCurrencies: Currency[] = generateMockCurrencies();

// Currency Service API functions
const CurrencyService = {
  // Get all currencies with pagination and filters
  getAllCurrencies: async (
    page: number = 1,
    pageSize: number = 10,
    filters?: CurrencyFilters
  ): Promise<CurrencyResponse> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    let filteredCurrencies = [...mockCurrencies];

    // Apply filters
    if (filters) {
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        filteredCurrencies = filteredCurrencies.filter(currency =>
          currency.name.toLowerCase().includes(searchLower) ||
          currency.code.toLowerCase().includes(searchLower) ||
          currency.symbol.toLowerCase().includes(searchLower)
        );
      }

      if (filters.isActive !== undefined) {
        filteredCurrencies = filteredCurrencies.filter(currency => 
          currency.isActive === filters.isActive
        );
      }

      if (filters.isBaseCurrency !== undefined) {
        filteredCurrencies = filteredCurrencies.filter(currency => 
          currency.isBaseCurrency === filters.isBaseCurrency
        );
      }

      if (filters.codeFilter) {
        filteredCurrencies = filteredCurrencies.filter(currency =>
          currency.code.toLowerCase().includes(filters.codeFilter.toLowerCase())
        );
      }
    }

    // Sort by code
    filteredCurrencies.sort((a, b) => a.code.localeCompare(b.code));

    // Pagination
    const totalItems = filteredCurrencies.length;
    const totalPages = Math.ceil(totalItems / pageSize);
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = filteredCurrencies.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      pagination: {
        current_page: page,
        page_size: pageSize,
        total_items: totalItems,
        total_pages: totalPages,
        has_next: page < totalPages,
        has_previous: page > 1
      },
      success: true,
      message: 'Currencies retrieved successfully'
    };
  },

  // Get currency by ID
  getCurrencyById: async (id: string): Promise<Currency | null> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockCurrencies.find(currency => currency.id === id) || null;
  },

  // Create new currency
  createCurrency: async (currencyData: CurrencyFormData): Promise<Currency> => {
    await new Promise(resolve => setTimeout(resolve, 500));

    // Check if currency code already exists
    const existingCurrency = mockCurrencies.find(c => c.code === currencyData.code);
    if (existingCurrency) {
      throw new Error(`Currency with code ${currencyData.code} already exists`);
    }

    // If setting as base currency, remove base flag from others
    if (currencyData.isBaseCurrency) {
      mockCurrencies = mockCurrencies.map(currency => ({
        ...currency,
        isBaseCurrency: false
      }));
    }

    const newCurrency: Currency = {
      id: `curr_${Date.now()}`,
      ...currencyData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      created_by: 'current_user',
      updated_by: 'current_user'
    };

    mockCurrencies.push(newCurrency);
    return newCurrency;
  },

  // Update currency
  updateCurrency: async (id: string, currencyData: Partial<CurrencyFormData>): Promise<Currency> => {
    await new Promise(resolve => setTimeout(resolve, 500));

    const currencyIndex = mockCurrencies.findIndex(currency => currency.id === id);
    if (currencyIndex === -1) {
      throw new Error('Currency not found');
    }

    // If setting as base currency, remove base flag from others
    if (currencyData.isBaseCurrency) {
      mockCurrencies = mockCurrencies.map(currency => ({
        ...currency,
        isBaseCurrency: currency.id === id
      }));
    }

    const updatedCurrency: Currency = {
      ...mockCurrencies[currencyIndex],
      ...currencyData,
      updated_at: new Date().toISOString(),
      updated_by: 'current_user'
    };

    mockCurrencies[currencyIndex] = updatedCurrency;
    return updatedCurrency;
  },

  // Delete currency
  deleteCurrency: async (id: string): Promise<boolean> => {
    await new Promise(resolve => setTimeout(resolve, 300));

    const currencyIndex = mockCurrencies.findIndex(currency => currency.id === id);
    if (currencyIndex === -1) {
      throw new Error('Currency not found');
    }

    const currency = mockCurrencies[currencyIndex];
    if (currency.isBaseCurrency) {
      throw new Error('Cannot delete base currency');
    }

    mockCurrencies.splice(currencyIndex, 1);
    return true;
  },

  // Get currency statistics
  getCurrencyStats: async () => {
    await new Promise(resolve => setTimeout(resolve, 200));

    const totalCurrencies = mockCurrencies.length;
    const activeCurrencies = mockCurrencies.filter(c => c.isActive).length;
    const inactiveCurrencies = totalCurrencies - activeCurrencies;
    const baseCurrency = mockCurrencies.find(c => c.isBaseCurrency)?.code || 'USD';
    const lastUpdated = mockCurrencies.reduce((latest, currency) => {
      return new Date(currency.updated_at) > new Date(latest) ? currency.updated_at : latest;
    }, mockCurrencies[0]?.updated_at || new Date().toISOString());

    return {
      totalCurrencies,
      activeCurrencies,
      inactiveCurrencies,
      baseCurrency,
      lastUpdated
    };
  },

  // Toggle currency active status
  toggleCurrencyStatus: async (id: string): Promise<Currency> => {
    await new Promise(resolve => setTimeout(resolve, 300));

    const currencyIndex = mockCurrencies.findIndex(currency => currency.id === id);
    if (currencyIndex === -1) {
      throw new Error('Currency not found');
    }

    const currency = mockCurrencies[currencyIndex];
    if (currency.isBaseCurrency && currency.isActive) {
      throw new Error('Cannot deactivate base currency');
    }

    const updatedCurrency: Currency = {
      ...currency,
      isActive: !currency.isActive,
      updated_at: new Date().toISOString(),
      updated_by: 'current_user'
    };

    mockCurrencies[currencyIndex] = updatedCurrency;
    return updatedCurrency;
  },

  // Get available currency codes for dropdown
  getAvailableCurrencyCodes: async (): Promise<{ code: string; name: string; symbol: string }[]> => {
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const usedCodes = mockCurrencies.map(c => c.code);
    return COMMON_CURRENCIES.filter(currency => !usedCodes.includes(currency.code));
  }
};

export default CurrencyService;
