'use client';

import { Currency, CurrencyUtils } from '../currency.model';

interface CurrencyViewProps {
  currency: Currency | null;
  onEdit: () => void;
  onClose: () => void;
}

export default function CurrencyView({ currency, onEdit, onClose }: CurrencyViewProps) {
  if (!currency) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No currency selected</p>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatAmount = (amount: number) => {
    return `${currency.symbol}${amount.toFixed(currency.decimalPlaces)}`;
  };

  return (
    <div className="space-y-6">
      {/* <PERSON><PERSON><PERSON>cy Header */}
      <div className="flex items-center space-x-4 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200">
        <div className="w-16 h-16 bg-gradient-to-br from-green-100 to-green-200 rounded-xl flex items-center justify-center">
          <span className="text-green-700 font-bold text-2xl">{currency.symbol}</span>
        </div>
        <div className="flex-1">
          <h2 className="text-2xl font-bold text-gray-900">{currency.name}</h2>
          <p className="text-lg text-gray-600">{currency.code}</p>
          <div className="flex items-center space-x-4 mt-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              currency.isActive
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}>
              <span className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
                currency.isActive ? 'bg-green-400' : 'bg-red-400'
              }`}></span>
              {currency.isActive ? 'Active' : 'Inactive'}
            </span>
            {currency.isBaseCurrency && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                <i className="ri-star-fill mr-1"></i>
                Base Currency
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Currency Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-500">Currency Code</label>
              <p className="text-lg font-semibold text-gray-900">{currency.code}</p>
              <p className="text-xs text-gray-500">ISO 4217 Standard</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Currency Name</label>
              <p className="text-lg font-semibold text-gray-900">{currency.name}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Symbol</label>
              <p className="text-lg font-semibold text-gray-900">{currency.symbol}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Decimal Places</label>
              <p className="text-lg font-semibold text-gray-900">{currency.decimalPlaces}</p>
              <p className="text-xs text-gray-500">
                Example: {formatAmount(123.456789)}
              </p>
            </div>
          </div>
        </div>

        {/* Exchange Rate Information */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Exchange Rate</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-500">Current Rate</label>
              <p className="text-2xl font-bold text-gray-900">{formatAmount(currency.amount)}</p>
              <p className="text-xs text-gray-500">
                {currency.isBaseCurrency ? 'Base currency rate' : 'Rate vs base currency'}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Rate Type</label>
              <p className="text-lg font-semibold text-gray-900">
                {currency.isBaseCurrency ? 'Base Currency' : 'Exchange Rate'}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Status</label>
              <div className="flex items-center space-x-2">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  currency.isActive
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {currency.isActive ? 'Active' : 'Inactive'}
                </span>
                {currency.isBaseCurrency && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    <i className="ri-star-fill mr-1"></i>
                    Base
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* System Information */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">System Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-500">Currency ID</label>
            <p className="text-sm font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded">
              {currency.id}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-500">Created Date</label>
            <p className="text-sm text-gray-900">{formatDate(currency.created_at)}</p>
            {currency.created_by && (
              <p className="text-xs text-gray-500">by {currency.created_by}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-500">Last Updated</label>
            <p className="text-sm text-gray-900">{formatDate(currency.updated_at)}</p>
            {currency.updated_by && (
              <p className="text-xs text-gray-500">by {currency.updated_by}</p>
            )}
          </div>
        </div>
      </div>

      {/* Example Conversions */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Example Amounts</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[1, 10, 100, 1000].map((amount) => (
            <div key={amount} className="text-center p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-500">Amount</p>
              <p className="text-lg font-semibold text-gray-900">
                {formatAmount(amount)}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
        <button
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
        >
          Close
        </button>
        <button
          onClick={onEdit}
          className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
        >
          <i className="ri-edit-line mr-2"></i>
          Edit Currency
        </button>
      </div>
    </div>
  );
}
